package convertz

import (
	"reflect"
	"sync"
)

// 转换缓存 - 提升重复转换的性能
type conversionCache struct {
	mu    sync.RWMutex
	cache map[cacheKey]reflect.Type
}

type cacheKey struct {
	src, dst reflect.Type
}

var globalCache = &conversionCache{
	cache: make(map[cacheKey]reflect.Type),
}

// 核心转换分发器 - 利用泛型的编译时优化
func convertTo[D any](src any, opts *Options) (D, error) {
	var zero D

	// 快速路径1: 相同类型直接返回 (零成本)
	if result, ok := src.(D); ok {
		return result, nil
	}

	// 快速路径2: nil值处理
	if src == nil {
		return zero, ErrInvalidSrc
	}

	// 获取编译时类型信息
	dstInfo := getTypeInfo[D]()

	// 类型分发 - 基于编译时信息，避免运行时反射
	switch dstInfo.Kind {
	case KindBool:
		result, err := convertToBoolean(src, opts)
		return any(result).(D), err

	case KindSignedInt, KindUnsignedInt, KindFloat:
		return convertToNumericTyped[D](src, opts, dstInfo)

	case KindComplex:
		return convertToComplexTyped[D](src, opts)

	case KindString, KindBytes, KindRunes:
		return convertToTextualTyped[D](src, opts)

	default:
		// 最后的反射兜底
		return reflectConvert[D](src, opts)
	}
}

// 布尔转换统一处理
func convertToBoolean(src any, opts *Options) (bool, error) {
	switch s := src.(type) {
	// 直接类型匹配 - 最快路径
	case bool:
		return s, nil

	// 数值类型转换 - 使用泛型函数
	case int:
		return numericToBoolean(s), nil
	case int8:
		return numericToBoolean(s), nil
	case int16:
		return numericToBoolean(s), nil
	case int32:
		return numericToBoolean(s), nil
	case int64:
		return numericToBoolean(s), nil
	case uint:
		return numericToBoolean(s), nil
	case uint8:
		return numericToBoolean(s), nil
	case uint16:
		return numericToBoolean(s), nil
	case uint32:
		return numericToBoolean(s), nil
	case uint64:
		return numericToBoolean(s), nil
	case uintptr:
		return numericToBoolean(s), nil
	case float32:
		return numericToBoolean(s), nil
	case float64:
		return numericToBoolean(s), nil

	// 复数类型转换
	case complex64:
		return complexToBoolean(s), nil
	case complex128:
		return complexToBoolean(s), nil

	// 文本类型转换
	case string:
		return textual2boolean[string, bool](s, opts.Boolean)
	case []byte:
		return textual2boolean[[]byte, bool](s, opts.Boolean)
	case []rune:
		return textual2boolean[[]rune, bool](s, opts.Boolean)

	default:
		// 反射兜底
		return reflectToBoolean(src, opts)
	}
}

// 数值转换统一处理 - 利用泛型约束
func convertToNumeric[D Numeric](src any, opts *Options, dstInfo TypeInfo) (D, error) {
	switch s := src.(type) {
	// 布尔转换
	case bool:
		return boolean2numeric[bool, D](s)

	// 数值间转换 - 使用优化的泛型函数
	case int:
		return numeric2numeric[int, D](s, opts.Numeric, dstInfo)
	case int8:
		return numeric2numeric[int8, D](s, opts.Numeric, dstInfo)
	case int16:
		return numeric2numeric[int16, D](s, opts.Numeric, dstInfo)
	case int32:
		return numeric2numeric[int32, D](s, opts.Numeric, dstInfo)
	case int64:
		return numeric2numeric[int64, D](s, opts.Numeric, dstInfo)
	case uint:
		return numeric2numeric[uint, D](s, opts.Numeric, dstInfo)
	case uint8:
		return numeric2numeric[uint8, D](s, opts.Numeric, dstInfo)
	case uint16:
		return numeric2numeric[uint16, D](s, opts.Numeric, dstInfo)
	case uint32:
		return numeric2numeric[uint32, D](s, opts.Numeric, dstInfo)
	case uint64:
		return numeric2numeric[uint64, D](s, opts.Numeric, dstInfo)
	case uintptr:
		return numeric2numeric[uintptr, D](s, opts.Numeric, dstInfo)
	case float32:
		return numeric2numeric[float32, D](s, opts.Numeric, dstInfo)
	case float64:
		return numeric2numeric[float64, D](s, opts.Numeric, dstInfo)

	// 复数转换
	case complex64:
		return complex2numeric[complex64, D](s)
	case complex128:
		return complex2numeric[complex128, D](s)

	// 文本转换
	case string:
		return textual2numeric[string, D](s, opts.Numeric, dstInfo)
	case []byte:
		return textual2numeric[[]byte, D](s, opts.Numeric, dstInfo)
	case []rune:
		return textual2numeric[[]rune, D](s, opts.Numeric, dstInfo)

	default:
		// 反射兜底
		v := reflect.ValueOf(src)
		return reflectToSignedInt[D](v, opts)
	}
}

// 复数转换统一处理
func convertToComplex[D Complex](src any, opts *Options) (D, error) {
	switch s := src.(type) {
	case bool:
		return boolean2complex[bool, D](s)
	case int:
		return numeric2complex[int, D](s)
	case int8:
		return numeric2complex[int8, D](s)
	case int16:
		return numeric2complex[int16, D](s)
	case int32:
		return numeric2complex[int32, D](s)
	case int64:
		return numeric2complex[int64, D](s)
	case uint:
		return numeric2complex[uint, D](s)
	case uint8:
		return numeric2complex[uint8, D](s)
	case uint16:
		return numeric2complex[uint16, D](s)
	case uint32:
		return numeric2complex[uint32, D](s)
	case uint64:
		return numeric2complex[uint64, D](s)
	case uintptr:
		return numeric2complex[uintptr, D](s)
	case float32:
		return numeric2complex[float32, D](s)
	case float64:
		return numeric2complex[float64, D](s)
	case complex64:
		return complex2complex[complex64, D](s)
	case complex128:
		return complex2complex[complex128, D](s)
	case string:
		return textual2complex[string, D](s)
	case []byte:
		return textual2complex[[]byte, D](s)
	case []rune:
		return textual2complex[[]rune, D](s)
	default:
		v := reflect.ValueOf(src)
		return reflectToComplex[D](v, opts)
	}
}

// 类型化转换函数 - 解决泛型约束问题
func convertToNumericTyped[D any](src any, opts *Options, dstInfo TypeInfo) (D, error) {
	var zero D

	// 使用类型断言来处理不同的数值类型
	switch any(zero).(type) {
	case int:
		result, err := convertToNumeric[int](src, opts, dstInfo)
		return any(result).(D), err
	case int8:
		result, err := convertToNumeric[int8](src, opts, dstInfo)
		return any(result).(D), err
	case int16:
		result, err := convertToNumeric[int16](src, opts, dstInfo)
		return any(result).(D), err
	case int32:
		result, err := convertToNumeric[int32](src, opts, dstInfo)
		return any(result).(D), err
	case int64:
		result, err := convertToNumeric[int64](src, opts, dstInfo)
		return any(result).(D), err
	case uint:
		result, err := convertToNumeric[uint](src, opts, dstInfo)
		return any(result).(D), err
	case uint8:
		result, err := convertToNumeric[uint8](src, opts, dstInfo)
		return any(result).(D), err
	case uint16:
		result, err := convertToNumeric[uint16](src, opts, dstInfo)
		return any(result).(D), err
	case uint32:
		result, err := convertToNumeric[uint32](src, opts, dstInfo)
		return any(result).(D), err
	case uint64:
		result, err := convertToNumeric[uint64](src, opts, dstInfo)
		return any(result).(D), err
	case uintptr:
		result, err := convertToNumeric[uintptr](src, opts, dstInfo)
		return any(result).(D), err
	case float32:
		result, err := convertToNumeric[float32](src, opts, dstInfo)
		return any(result).(D), err
	case float64:
		result, err := convertToNumeric[float64](src, opts, dstInfo)
		return any(result).(D), err
	default:
		return zero, ErrUnsupported
	}
}

func convertToComplexTyped[D any](src any, opts *Options) (D, error) {
	var zero D

	switch any(zero).(type) {
	case complex64:
		result, err := convertToComplex[complex64](src, opts)
		return any(result).(D), err
	case complex128:
		result, err := convertToComplex[complex128](src, opts)
		return any(result).(D), err
	default:
		return zero, ErrUnsupported
	}
}

func convertToTextualTyped[D any](src any, opts *Options) (D, error) {
	var zero D

	switch any(zero).(type) {
	case string:
		result, err := convertToTextual[string](src, opts)
		return any(result).(D), err
	case []byte:
		result, err := convertToTextual[[]byte](src, opts)
		return any(result).(D), err
	case []rune:
		result, err := convertToTextual[[]rune](src, opts)
		return any(result).(D), err
	default:
		return zero, ErrUnsupported
	}
}

// 辅助函数 - 处理类型约束问题
func convertToNumericAny(src any, opts *Options, dstInfo TypeInfo) (any, error) {
	switch dstInfo.Kind {
	case KindSignedInt:
		switch dstInfo.BitSize {
		case 8:
			return convertToNumeric[int8](src, opts, dstInfo)
		case 16:
			return convertToNumeric[int16](src, opts, dstInfo)
		case 32:
			return convertToNumeric[int32](src, opts, dstInfo)
		case 64:
			return convertToNumeric[int64](src, opts, dstInfo)
		default:
			return convertToNumeric[int](src, opts, dstInfo)
		}
	case KindUnsignedInt:
		switch dstInfo.BitSize {
		case 8:
			return convertToNumeric[uint8](src, opts, dstInfo)
		case 16:
			return convertToNumeric[uint16](src, opts, dstInfo)
		case 32:
			return convertToNumeric[uint32](src, opts, dstInfo)
		case 64:
			return convertToNumeric[uint64](src, opts, dstInfo)
		default:
			return convertToNumeric[uint](src, opts, dstInfo)
		}
	case KindFloat:
		if dstInfo.BitSize == 32 {
			return convertToNumeric[float32](src, opts, dstInfo)
		}
		return convertToNumeric[float64](src, opts, dstInfo)
	}
	return nil, ErrUnsupported
}

func convertToComplexAny(src any, opts *Options) (any, error) {
	// 默认使用 complex128，如果需要 complex64 会在上层处理
	return convertToComplex[complex128](src, opts)
}

func convertToTextualAny(src any, opts *Options) (any, error) {
	// 默认转换为 string
	return convertToTextual[string](src, opts)
}

// 文本转换统一处理
func convertToTextual[D Textual](src any, opts *Options) (D, error) {
	switch s := src.(type) {
	case bool:
		return boolean2textual[bool, D](s)
	case int:
		return numeric2textual[int, D](s)
	case int8:
		return numeric2textual[int8, D](s)
	case int16:
		return numeric2textual[int16, D](s)
	case int32:
		return numeric2textual[int32, D](s)
	case int64:
		return numeric2textual[int64, D](s)
	case uint:
		return numeric2textual[uint, D](s)
	case uint8:
		return numeric2textual[uint8, D](s)
	case uint16:
		return numeric2textual[uint16, D](s)
	case uint32:
		return numeric2textual[uint32, D](s)
	case uint64:
		return numeric2textual[uint64, D](s)
	case uintptr:
		return numeric2textual[uintptr, D](s)
	case float32:
		return numeric2textual[float32, D](s)
	case float64:
		return numeric2textual[float64, D](s)
	case complex64:
		return complex2textual[complex64, D](s)
	case complex128:
		return complex2textual[complex128, D](s)
	case string:
		return textual2textual[string, D](s)
	case []byte:
		return textual2textual[[]byte, D](s)
	case []rune:
		return textual2textual[[]rune, D](s)
	default:
		return reflectToTextual[D](src, opts)
	}
}
