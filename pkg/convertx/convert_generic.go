package convertz

import (
	"math"
	"reflect"
	"strconv"
	"strings"
	"unsafe"
)

// ============================================================================
// 布尔转换函数 - 利用泛型约束优化
// ============================================================================

// 数值到布尔的泛型转换 - 编译时优化
func numericToBoolean[T Numeric](src T) bool {
	return src != 0
}

// 复数到布尔的转换
func complexToBoolean[T Complex](src T) bool {
	return src != 0
}

// 布尔到数值的泛型转换
func boolean2numeric[S Boolean, D Numeric](src S) (D, error) {
	if bool(src) {
		return D(1), nil
	}
	return D(0), nil
}

// 布尔到文本的转换
func boolean2textual[S Boolean, D Textual](src S) (D, error) {
	if bool(src) {
		return stringToTextual[D]("true"), nil
	}
	return stringToTextual[D]("false"), nil
}

// 布尔到复数的转换
func boolean2complex[S Boolean, D Complex](src S) (D, error) {
	if bool(src) {
		return D(1 + 0i), nil
	}
	return D(0 + 0i), nil
}

// ============================================================================
// 数值转换函数 - 高性能实现
// ============================================================================

// 数值间转换 - 带溢出检查的优化版本
func numeric2numeric[S Numeric, D Numeric](src S, opts *NumericOptions, dstInfo TypeInfo) (D, error) {
	// 严格模式下检查溢出
	if opts != nil && opts.Strict {
		if err := checkOverflow(float64(src), dstInfo); err != nil {
			return D(0), err
		}
	}
	return D(src), nil
}

// 数值到文本的高性能转换
func numeric2textual[S Numeric, D Textual](src S) (D, error) {
	var str string

	switch s := any(src).(type) {
	case int:
		str = strconv.Itoa(s)
	case int8:
		str = strconv.FormatInt(int64(s), 10)
	case int16:
		str = strconv.FormatInt(int64(s), 10)
	case int32:
		str = strconv.FormatInt(int64(s), 10)
	case int64:
		str = strconv.FormatInt(s, 10)
	case uint:
		str = strconv.FormatUint(uint64(s), 10)
	case uint8:
		str = strconv.FormatUint(uint64(s), 10)
	case uint16:
		str = strconv.FormatUint(uint64(s), 10)
	case uint32:
		str = strconv.FormatUint(uint64(s), 10)
	case uint64:
		str = strconv.FormatUint(s, 10)
	case uintptr:
		str = strconv.FormatUint(uint64(s), 10)
	case float32:
		str = strconv.FormatFloat(float64(s), 'g', -1, 32)
	case float64:
		str = strconv.FormatFloat(s, 'g', -1, 64)
	default:
		return D(""), ErrUnsupported
	}

	return stringToTextual[D](str), nil
}

// 数值到复数的转换
func numeric2complex[S Numeric, D Complex](src S) (D, error) {
	return D(complex(float64(src), 0)), nil
}

// ============================================================================
// 文本转换函数 - 零拷贝优化
// ============================================================================

// 文本到布尔的转换 - 优化的字符串处理
func textual2boolean[S Textual, D Boolean](src S, opts *BooleanOptions) (D, error) {
	str := strings.ToLower(textualToString(src))

	// 检查自定义选项
	if opts != nil {
		if opts.TrueSet != nil {
			if _, ok := opts.TrueSet[str]; ok {
				return D(true), nil
			}
		}
		if opts.FalseSet != nil {
			if _, ok := opts.FalseSet[str]; ok {
				return D(false), nil
			}
		}
	}

	// 标准布尔值解析
	switch str {
	case "true", "1", "yes", "on", "t", "y":
		return D(true), nil
	case "false", "0", "no", "off", "f", "n":
		return D(false), nil
	default:
		if result, err := strconv.ParseBool(str); err == nil {
			return D(result), nil
		}
		return D(false), ErrUnsupported
	}
}

// 文本到数值的转换 - 类型特化优化
func textual2numeric[S Textual, D Numeric](src S, opts *NumericOptions, dstInfo TypeInfo) (D, error) {
	str := textualToString(src)

	switch dstInfo.Kind {
	case KindSignedInt:
		val, err := strconv.ParseInt(str, 10, dstInfo.BitSize)
		if err != nil {
			return D(0), ErrUnsupported
		}
		return D(val), nil

	case KindUnsignedInt:
		val, err := strconv.ParseUint(str, 10, dstInfo.BitSize)
		if err != nil {
			return D(0), ErrUnsupported
		}
		return D(val), nil

	case KindFloat:
		val, err := strconv.ParseFloat(str, dstInfo.BitSize)
		if err != nil {
			return D(0), ErrUnsupported
		}
		return D(val), nil

	default:
		return D(0), ErrUnsupported
	}
}

// 文本到复数的转换
func textual2complex[S Textual, D Complex](src S) (D, error) {
	str := textualToString(src)

	var bitSize int
	var zero D
	switch any(zero).(type) {
	case complex64:
		bitSize = 64
	case complex128:
		bitSize = 128
	default:
		return D(0), ErrUnsupported
	}

	val, err := strconv.ParseComplex(str, bitSize)
	if err != nil {
		return D(0), ErrUnsupported
	}

	return D(val), nil
}

// 文本间转换 - 零拷贝优化
func textual2textual[S Textual, D Textual](src S) (D, error) {
	return textualToTextual[S, D](src), nil
}

// ============================================================================
// 复数转换函数
// ============================================================================

// 复数到数值的转换 - 取实部
func complex2numeric[S Complex, D Numeric](src S) (D, error) {
	return D(real(complex128(src))), nil
}

// 复数到文本的转换
func complex2textual[S Complex, D Textual](src S) (D, error) {
	var bitSize int
	switch any(src).(type) {
	case complex64:
		bitSize = 64
	case complex128:
		bitSize = 128
	default:
		return D(""), ErrUnsupported
	}

	str := strconv.FormatComplex(complex128(src), 'g', -1, bitSize)
	return stringToTextual[D](str), nil
}

// 复数间转换
func complex2complex[S Complex, D Complex](src S) (D, error) {
	return D(src), nil
}

// ============================================================================
// 辅助函数 - 零拷贝和性能优化
// ============================================================================

// 零拷贝文本类型转换
func textualToString[S Textual](src S) string {
	switch s := any(src).(type) {
	case string:
		return s
	case []byte:
		// 零拷贝转换 - 使用unsafe实现高性能
		return unsafe.String(unsafe.SliceData(s), len(s))
	case []rune:
		return string(s)
	default:
		return ""
	}
}

// 字符串到文本类型的零拷贝转换
func stringToTextual[D Textual](s string) D {
	var zero D
	switch any(zero).(type) {
	case string:
		return any(s).(D)
	case []byte:
		// 零拷贝转换
		return any(unsafe.Slice(unsafe.StringData(s), len(s))).(D)
	case []rune:
		return any([]rune(s)).(D)
	default:
		return zero
	}
}

// 文本类型间的零拷贝转换
func textualToTextual[S Textual, D Textual](src S) D {
	var srcZero S
	var dstZero D

	// 相同类型直接返回
	if reflect.TypeOf(srcZero) == reflect.TypeOf(dstZero) {
		return any(src).(D)
	}

	// 转换为字符串再转换为目标类型
	str := textualToString(src)
	return stringToTextual[D](str)
}

// 溢出检查 - 高性能实现
func checkOverflow(val float64, dstInfo TypeInfo) error {
	if math.IsNaN(val) || math.IsInf(val, 0) {
		return ErrOverflow
	}

	switch dstInfo.Kind {
	case KindSignedInt:
		max := float64(int64(1)<<(dstInfo.BitSize-1) - 1)
		min := -max - 1
		if val < min || val > max || val != math.Trunc(val) {
			return ErrOverflow
		}
	case KindUnsignedInt:
		max := float64(uint64(1)<<dstInfo.BitSize - 1)
		if val < 0 || val > max || val != math.Trunc(val) {
			return ErrOverflow
		}
	case KindFloat:
		if dstInfo.BitSize == 32 && math.Abs(val) > math.MaxFloat32 {
			return ErrOverflow
		}
	}

	return nil
}
