package converts

import (
	"reflect"
	"sync"
)

type typeInfo[T any] struct {
	zero   T
	value  T
	rtype  reflect.Type
	rkind  reflect.Kind
	rvalue reflect.Value
}

func (info *typeInfo[T]) isBoolean() bool {
	return info.rkind == reflect.Bool
}

func (info *typeInfo[T]) isNumeric() bool {
	return info.rkind >= reflect.Int && info.rkind <= reflect.Float64
}

func (info *typeInfo[T]) isComplex() bool {
	return info.rkind == reflect.Complex64 || info.rkind == reflect.Complex128
}

func (info *typeInfo[T]) isTextual() bool {
	return info.rkind == reflect.String || info.rkind == reflect.Slice
}

type globalCache = sync.Map

var cache globalCache

func getTypeInfo[T any](value T) *typeInfo[T] {
	var zero T
	if info, ok := cache.Load(&zero); ok {
		return info.(*typeInfo[T])
	}
	info := &typeInfo[T]{zero: zero, value: value}
	switch any(zero).(type) {
	case bool:
		info.rkind = reflect.Bool
	case int:
		info.rkind = reflect.Int
	case int8:
		info.rkind = reflect.Int8
	case int16:
		info.rkind = reflect.Int16
	case int32:
		info.rkind = reflect.Int32
	case int64:
		info.rkind = reflect.Int64
	case uint:
		info.rkind = reflect.Uint
	case uint8:
		info.rkind = reflect.Uint8
	case uint16:
		info.rkind = reflect.Uint16
	case uint32:
		info.rkind = reflect.Uint32
	case uint64:
		info.rkind = reflect.Uint64
	case uintptr:
		info.rkind = reflect.Uintptr
	case float32:
		info.rkind = reflect.Float32
	case float64:
		info.rkind = reflect.Float64
	case complex64:
		info.rkind = reflect.Complex64
	case complex128:
		info.rkind = reflect.Complex128
	case string:
		info.rkind = reflect.String
	case []byte:
		info.rkind = reflect.Slice
	case []rune:
		info.rkind = reflect.Slice
	}
	cache.Store(&zero, info)
	return info
}
