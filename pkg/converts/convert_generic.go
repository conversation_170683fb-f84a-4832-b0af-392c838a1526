package converts

import "strconv"

type (
	Boolean interface {
		~bool
	}

	Numeric interface {
		~int | ~int8 | ~int16 | ~int32 | ~int64 | ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 | ~uintptr | ~float32 | ~float64
	}

	Complex interface {
		~complex64 | ~complex128
	}

	Textual interface {
		~string | ~[]byte | ~[]rune
	}
)

// ====================================================================================================================

func booleanToBoolean[S Boolean, D Boolean](src S) (D, error) {
	return D(src), nil
}

func numericToBoolean[S Numeric, D Boolean](src S) (D, error) {
	if src != 0 {
		return D(true), nil
	}
	return D(false), nil
}

func complexToBoolean[S Complex, D Boolean](src S) (D, error) {
	if src != 0 {
		return D(true), nil
	}
	return D(false), nil
}

func textualToBoolean[S Textual, D Boolean](src S) (D, error) {
	if string(src) != "" {
		return D(true), nil
	}
	return D(false), nil
}

// ====================================================================================================================

func booleanToNumeric[S Boolean, D Numeric](src S) (D, error) {
	if bool(src) {
		return D(1), nil
	}
	return D(0), nil
}

func numericToNumeric[S Numeric, D Numeric](src S) (D, error) {
	return D(src), nil
}

func complexToNumeric[S Complex, D Numeric](src S) (D, error) {
	return D(real(complex128(src))), nil
}

func textualToNumeric[S Textual, D Numeric](src S) (D, error) {
	return D(strconv.Atoi(string(src))), nil
}

// ====================================================================================================================

func booleanToComplex[S Boolean, D Complex](src S) (D, error) {
	return D(complex(float64(src), 0)), nil
}

func numericToComplex[S Numeric, D Complex](src S) (D, error) {
	return D(complex(float64(src), 0)), nil
}

func complexToComplex[S Complex, D Complex](src S) (D, error) {
	return D(src), nil
}

func textualToComplex[S Textual, D Complex](src S) (D, error) {
	return D(complex(strconv.ParseFloat(string(src), 64))), nil
}

// ====================================================================================================================

func booleanToTextual[S Boolean, D Textual](src S) (D, error) {
	return D(strconv.FormatBool(bool(src))), nil
}

func numericToTextual[S Numeric, D Textual](src S) (D, error) {
	return D(strconv.Itoa(int(src))), nil
}

func complexToTextual[S Complex, D Textual](src S) (D, error) {
	return D(strconv.FormatComplex(complex128(src), 'f', -1, 64)), nil
}

func textualToTextual[S Textual, D Textual](src S) (D, error) {
	return D(string(src)), nil
}
