package converts

import (
	"reflect"
	"strconv"
	"strings"
)

type (
	Boolean interface {
		~bool
	}

	boolean_t[T Boolean] struct{ val T }

	BooleanOptions struct {
		TrueSet  map[string]struct{} // 真值集合
		FalseSet map[string]struct{} // 假值集合
	}
)

// ================================ 泛型转换函数 ================================

func numeric2boolean[S Numeric, D Boolean](src S) (D, error) {
	return D(src != 0), nil
}

func complex2boolean[S Complex, D Boolean](src S) (D, error) {
	return D(src != 0), nil
}

func textual2boolean[S Textual, D Boolean](src S, opt *BooleanOptions) (D, error) {
	str := strings.ToLower(textual_t[S]{src}.toString())
	if opt != nil {
		if _, ok := opt.TrueSet[str]; ok {
			return D(true), nil
		}
		if _, ok := opt.FalseSet[str]; ok {
			return D(false), nil
		}
	}
	if dst, err := strconv.ParseBool(str); err == nil {
		return D(dst), nil
	}
	return D(false), ErrUnsupported
}

func ToBoolean[D Boolean](src any, opt *BooleanOptions) (D, error) {
	switch s := any(src).(type) {
	case bool:
		return D(s), nil
	case int:
		return numeric2boolean[int, D](s)
	case int8:
		return numeric2boolean[int8, D](s)
	case int16:
		return numeric2boolean[int16, D](s)
	case int32:
		return numeric2boolean[int32, D](s)
	case int64:
		return numeric2boolean[int64, D](s)
	case uint:
		return numeric2boolean[uint, D](s)
	case uint8:
		return numeric2boolean[uint8, D](s)
	case uint16:
		return numeric2boolean[uint16, D](s)
	case uint32:
		return numeric2boolean[uint32, D](s)
	case uint64:
		return numeric2boolean[uint64, D](s)
	case uintptr:
		return numeric2boolean[uintptr, D](s)
	case float32:
		return numeric2boolean[float32, D](s)
	case float64:
		return numeric2boolean[float64, D](s)
	case complex64:
		return complex2boolean[complex64, D](s)
	case complex128:
		return complex2boolean[complex128, D](s)
	case string:
		return textual2boolean[string, D](s, opt)
	case []byte:
		return textual2boolean[[]byte, D](s, opt)
	case []rune:
		return textual2boolean[[]rune, D](s, opt)
	default:
		v := reflect.ValueOf(src)
		switch {
		case v.Kind() == reflect.Bool:
			return D(v.Bool()), nil
		case v.CanInt():
			return numeric2boolean[int64, D](v.Int())
		case v.CanUint():
			return numeric2boolean[uint64, D](v.Uint())
		case v.CanFloat():
			return numeric2boolean[float64, D](v.Float())
		case v.CanComplex():
			return complex2boolean[complex128, D](v.Complex())
		case v.Kind() == reflect.String:
			return textual2boolean[string, D](v.String(), opt)
		case v.Kind() == reflect.Slice:
			if v.Type().Elem().Kind() == reflect.Uint8 {
				return textual2boolean[[]byte, D](v.Bytes(), opt)
			} else if v.Type().Elem().Kind() == reflect.Int32 {
				return textual2boolean[[]rune, D](v.Interface().([]rune), opt)
			}
		}
		return D(false), ErrUnsupported
	}
}
