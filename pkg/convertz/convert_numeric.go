
package converts

import (
	"math"
	"math/bits"
	"reflect"
	"strconv"
)

type (
	Numeric interface {
		~int | ~int8 | ~int16 | ~int32 | ~int64 | ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 | ~uintptr | ~float32 | ~float64
	}

	numeric_t[T Numeric] struct{ val T }

	NumericOptions struct {
		Strict bool // 是否严格模式
	}
)

func (t numeric_t[T]) bitSize() int {
	switch any(t.val).(type) {
	case int8, uint8:
		return 8
	case int16, uint16:
		return 16
	case int32, uint32, float32:
		return 32
	case int64, uint64, float64:
		return 64
	case int, uint, uintptr:
		return bits.UintSize
	default:
		return 0
	}
}

func (t numeric_t[T]) isInt() bool {
	switch any(t.val).(type) {
	case int, int8, int16, int32, int64:
		return true
	default:
		return false
	}
}

func (t numeric_t[T]) isUint() bool {
	switch any(t.val).(type) {
	case uint, uint8, uint16, uint32, uint64, uintptr:
		return true
	default:
		return false
	}
}

func (t numeric_t[T]) isFloat() bool {
	switch any(t.val).(type) {
	case float32, float64:
		return true
	default:
		return false
	}
}

func (t numeric_t[T]) overflow(src float64) bool {
	if math.IsNaN(src) || math.IsInf(src, 0) {
		return true
	}
	bitSize := t.bitSize()
	switch {
	case t.isInt():
		cutoff := float64(uint64(1) << (bitSize - 1))
		return src != math.Trunc(src) || src < -cutoff || src >= cutoff
	case t.isUint():
		return src < 0 || src != math.Trunc(src) || src > float64((uint64(1)<<bitSize)-1)
	case t.isFloat():
		return bitSize == 32 && math.Abs(src) > math.MaxFloat32
	}
	return true
}

// ================================ 泛型转换函数 ================================

func boolean2numeric[S Boolean, D Numeric](src S) (D, error) {
	if bool(src) {
		return D(1), nil
	}
	return D(0), nil
}

func complex2numeric[S Complex, D Numeric](src S) (D, error) {
	return D(real(complex128(src))), nil
}

func numeric2numeric[S Numeric, D Numeric](src S, opt *NumericOptions) (D, error) {
	s := numeric_t[S]{src}
	if opt != nil && opt.Strict && s.overflow(float64(src)) {
		return D(0), ErrOverflow
	}
	return D(src), nil
}

func textual2numeric[S Textual, D Numeric](src S, opt *NumericOptions) (D, error) {
	s, d := textual_t[S]{src}, numeric_t[D]{}
	switch {
	case d.isInt():
		if val, err := strconv.ParseInt(s.toString(), 10, d.bitSize()); err == nil {
			return D(val), nil
		}
	case d.isUint():
		if val, err := strconv.ParseUint(s.toString(), 10, d.bitSize()); err == nil {
			return D(val), nil
		}
	case d.isFloat():
		if val, err := strconv.ParseFloat(s.toString(), d.bitSize()); err == nil {
			return D(val), nil
		}
	}
	return D(0), ErrUnsupported
}

func ToNumeric[D Numeric](src any, opt *NumericOptions) (D, error) {
	switch s := any(src).(type) {
	case bool:
		return boolean2numeric[bool, D](s)
	case int:
		return numeric2numeric[int, D](s, opt)
	case int8:
		return numeric2numeric[int8, D](s, opt)
	case int16:
		return numeric2numeric[int16, D](s, opt)
	case int32:
		return numeric2numeric[int32, D](s, opt)
	case int64:
		return numeric2numeric[int64, D](s, opt)
	case uint:
		return numeric2numeric[uint, D](s, opt)
	case uint8:
		return numeric2numeric[uint8, D](s, opt)
	case uint16:
		return numeric2numeric[uint16, D](s, opt)
	case uint32:
		return numeric2numeric[uint32, D](s, opt)
	case uint64:
		return numeric2numeric[uint64, D](s, opt)
	case uintptr:
		return numeric2numeric[uintptr, D](s, opt)
	case float32:
		return numeric2numeric[float32, D](s, opt)
	case float64:
		return numeric2numeric[float64, D](s, opt)
	case complex64:
		return complex2numeric[complex64, D](s)
	case complex128:
		return complex2numeric[complex128, D](s)
	case string:
		return textual2numeric[string, D](s, opt)
	case []byte:
		return textual2numeric[[]byte, D](s, opt)
	case []rune:
		return textual2numeric[[]rune, D](s, opt)
	default:
		v := reflect.ValueOf(src)
		switch {
		case v.Kind() == reflect.Bool:
			return boolean2numeric[bool, D](v.Bool())
		case v.CanInt():
			return numeric2numeric[int64, D](v.Int(), opt)
		case v.CanUint():
			return numeric2numeric[uint64, D](v.Uint(), opt)
		case v.CanFloat():
			return numeric2numeric[float64, D](v.Float(), opt)
		case v.CanComplex():
			return complex2numeric[complex128, D](v.Complex())
		case v.Kind() == reflect.String:
			return textual2numeric[string, D](v.String(), opt)
		case v.Kind() == reflect.Slice:
			if v.Type().Elem().Kind() == reflect.Uint8 {
				return textual2numeric[[]byte, D](v.Bytes(), opt)
			} else if v.Type().Elem().Kind() == reflect.Int32 {
				return textual2numeric[[]rune, D](v.Interface().([]rune), opt)
			}
		}
		return D(0), ErrUnsupported
	}
}
