package converts

import (
	"reflect"
	"strconv"
)

type (
	Textual interface {
		~string | ~[]byte | ~[]rune
	}

	textual_t[T Textual] struct{ val T }

	TextualOptions struct{}
)

func (t textual_t[T]) toString() string {
	return string(t.val)
}

// ================================ 泛型转换函数 ================================

func boolean2textual[S Boolean, D Textual](src S) (D, error) {
	if bool(src) {
		return D("true"), nil
	}
	return D("false"), nil
}

func numeric2textual[S Numeric, D Textual](src S) (D, error) {
	s := numeric_t[S]{src}
	switch {
	case s.isInt():
		return D(strconv.FormatInt(int64(src), 10)), nil
	case s.isUint():
		return D(strconv.FormatUint(uint64(src), 10)), nil
	case s.isFloat():
		return D(strconv.FormatFloat(float64(src), 'f', -1, s.bitSize())), nil
	default:
		return D(""), ErrUnsupported
	}
}

func complex2textual[S Complex, D Textual](src S) (D, error) {
	str := strconv.FormatComplex(complex128(src), 'g', -1, complex_t[S]{}.bitSize())
	return D(str), nil
}

func textual2textual[S Textual, D Textual](src S) (D, error) {
	return D(textual_t[S]{src}.toString()), nil
}

func ToTextual[D Textual](src any, opt *TextualOptions) (D, error) {
	switch s := any(src).(type) {
	case bool:
		return boolean2textual[bool, D](s)
	case int:
		return numeric2textual[int, D](s)
	case int8:
		return numeric2textual[int8, D](s)
	case int16:
		return numeric2textual[int16, D](s)
	case int32:
		return numeric2textual[int32, D](s)
	case int64:
		return numeric2textual[int64, D](s)
	case uint:
		return numeric2textual[uint, D](s)
	case uint8:
		return numeric2textual[uint8, D](s)
	case uint16:
		return numeric2textual[uint16, D](s)
	case uint32:
		return numeric2textual[uint32, D](s)
	case uint64:
		return numeric2textual[uint64, D](s)
	case uintptr:
		return numeric2textual[uintptr, D](s)
	case float32:
		return numeric2textual[float32, D](s)
	case float64:
		return numeric2textual[float64, D](s)
	case complex64:
		return complex2textual[complex64, D](s)
	case complex128:
		return complex2textual[complex128, D](s)
	case string:
		return textual2textual[string, D](s)
	case []byte:
		return textual2textual[[]byte, D](s)
	case []rune:
		return textual2textual[[]rune, D](s)
	default:
		v := reflect.ValueOf(src)
		switch {
		case v.Kind() == reflect.Bool:
			return boolean2textual[bool, D](v.Bool())
		case v.CanInt():
			return numeric2textual[int64, D](v.Int())
		case v.CanUint():
			return numeric2textual[uint64, D](v.Uint())
		case v.CanFloat():
			return numeric2textual[float64, D](v.Float())
		case v.CanComplex():
			return complex2textual[complex128, D](v.Complex())
		case v.Kind() == reflect.String:
			return textual2textual[string, D](v.String())
		case v.Kind() == reflect.Slice:
			if v.Type().Elem().Kind() == reflect.Uint8 {
				bytes := v.Bytes()
				return textual2textual[[]byte, D](bytes)
			} else if v.Type().Elem().Kind() == reflect.Int32 {
				runes := v.Interface().([]rune)
				return textual2textual[[]rune, D](runes)
			}
		}
		return D(""), ErrUnsupported
	}
}
